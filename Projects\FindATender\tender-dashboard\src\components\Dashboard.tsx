'use client';

import React, { useState, useEffect } from 'react';
import { ProcessedTender } from '@/types/tender';
import { apiClient } from '@/services/apiClient';
import { filterService, FILTER_CATEGORIES } from '@/services/filterService';
import { TenderTable } from './TenderTable';
import { FilterPanel } from './FilterPanel';
import { StatsPanel } from './StatsPanel';
import { SearchBar } from './SearchBar';
import { RotateCcw, AlertCircle } from 'lucide-react';

export function Dashboard() {
  const [tenders, setTenders] = useState<ProcessedTender[]>([]);
  const [filteredTenders, setFilteredTenders] = useState<ProcessedTender[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  // Filter states
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'relevance' | 'date' | 'value' | 'closing'>('relevance');

  const fetchTenders = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiClient.fetchTenders({ daysBack: 7, limit: 100 });
      setTenders(response.tenders);
      setLastUpdated(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch tenders');
      console.error('Error fetching tenders:', err);
    } finally {
      setLoading(false);
    }
  };

  // Apply filters whenever tenders or filter criteria change
  useEffect(() => {
    let filtered = [...tenders];

    // Apply category filters
    if (selectedCategories.length > 0) {
      filtered = filterService.filterTenders(filtered, selectedCategories);
    }

    // Apply search filter
    if (searchTerm.trim()) {
      filtered = filterService.searchTenders(filtered, searchTerm);
    }

    // Apply sorting
    filtered = filterService.sortTenders(filtered, sortBy);

    setFilteredTenders(filtered);
  }, [tenders, selectedCategories, searchTerm, sortBy]);

  // Initial load
  useEffect(() => {
    fetchTenders();
  }, []);

  // Auto-refresh every 30 minutes
  useEffect(() => {
    const interval = setInterval(fetchTenders, 30 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const handleCategoryToggle = (categoryId: string) => {
    setSelectedCategories(prev => 
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const handleRefresh = () => {
    fetchTenders();
  };

  const filterStats = filterService.getFilterStats(tenders);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                UK Tender Dashboard
              </h1>
              <p className="text-sm text-gray-600">
                Sustainability & Environmental Opportunities
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              {lastUpdated && (
                <span className="text-sm text-gray-500">
                  Last updated: {lastUpdated.toLocaleTimeString()}
                </span>
              )}
              <button
                onClick={handleRefresh}
                disabled={loading}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                <RotateCcw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Error loading tenders
                </h3>
                <p className="mt-1 text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Stats Panel */}
        <StatsPanel 
          totalTenders={tenders.length}
          filteredTenders={filteredTenders.length}
          filterStats={filterStats}
          loading={loading}
        />

        {/* Search and Filters */}
        <div className="mb-6 space-y-4">
          <SearchBar 
            value={searchTerm}
            onChange={setSearchTerm}
            placeholder="Search tenders by title, description, buyer..."
          />
          
          <FilterPanel
            categories={FILTER_CATEGORIES}
            selectedCategories={selectedCategories}
            onCategoryToggle={handleCategoryToggle}
            filterStats={filterStats}
            sortBy={sortBy}
            onSortChange={setSortBy}
          />
        </div>

        {/* Tender Table */}
        <TenderTable 
          tenders={filteredTenders}
          loading={loading}
        />
      </div>
    </div>
  );
}
