'use client';

import React from 'react';
import { FilterCategory } from '@/services/filterService';
import { ChevronDown } from 'lucide-react';

interface FilterPanelProps {
  categories: FilterCategory[];
  selectedCategories: string[];
  onCategoryToggle: (categoryId: string) => void;
  filterStats: Record<string, number>;
  sortBy: 'relevance' | 'date' | 'value' | 'closing';
  onSortChange: (sortBy: 'relevance' | 'date' | 'value' | 'closing') => void;
}

export function FilterPanel({
  categories,
  selectedCategories,
  onCategoryToggle,
  filterStats,
  sortBy,
  onSortChange
}: FilterPanelProps) {
  const totalSelected = selectedCategories.length;
  const allSelected = totalSelected === categories.length;
  const noneSelected = totalSelected === 0;

  const handleSelectAll = () => {
    if (allSelected) {
      // Deselect all
      selectedCategories.forEach(id => onCategoryToggle(id));
    } else {
      // Select all unselected categories
      categories.forEach(category => {
        if (!selectedCategories.includes(category.id)) {
          onCategoryToggle(category.id);
        }
      });
    }
  };

  const handleClearAll = () => {
    selectedCategories.forEach(id => onCategoryToggle(id));
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        {/* Category Filters */}
        <div className="flex-1">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-900">
              Filter by Category
            </h3>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleSelectAll}
                className="text-xs text-blue-600 hover:text-blue-800"
              >
                {allSelected ? 'Deselect All' : 'Select All'}
              </button>
              {!noneSelected && (
                <>
                  <span className="text-gray-300">|</span>
                  <button
                    onClick={handleClearAll}
                    className="text-xs text-gray-600 hover:text-gray-800"
                  >
                    Clear All
                  </button>
                </>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
            {categories.map((category) => {
              const isSelected = selectedCategories.includes(category.id);
              const count = filterStats[category.id] || 0;
              
              return (
                <button
                  key={category.id}
                  onClick={() => onCategoryToggle(category.id)}
                  className={`
                    relative flex items-center justify-between p-3 rounded-lg border-2 transition-all
                    ${isSelected 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'
                    }
                  `}
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{category.icon}</span>
                    <div className="text-left">
                      <p className="text-sm font-medium text-gray-900">
                        {category.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {count} tender{count !== 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>
                  
                  {isSelected && (
                    <div className="absolute top-2 right-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    </div>
                  )}
                </button>
              );
            })}
          </div>
        </div>

        {/* Sort Options */}
        <div className="lg:ml-6">
          <label htmlFor="sort-select" className="block text-sm font-medium text-gray-900 mb-2">
            Sort by
          </label>
          <div className="relative">
            <select
              id="sort-select"
              value={sortBy}
              onChange={(e) => onSortChange(e.target.value as any)}
              className="appearance-none bg-white border border-gray-300 rounded-md px-3 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="relevance">Relevance Score</option>
              <option value="date">Publication Date</option>
              <option value="value">Contract Value</option>
              <option value="closing">Closing Date</option>
            </select>
            <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
          </div>
        </div>
      </div>

      {/* Active Filters Summary */}
      {!noneSelected && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Active filters:</span>
              <div className="flex flex-wrap gap-1">
                {selectedCategories.map(categoryId => {
                  const category = categories.find(c => c.id === categoryId);
                  if (!category) return null;
                  
                  return (
                    <span
                      key={categoryId}
                      className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium ${category.color}`}
                    >
                      <span className="mr-1">{category.icon}</span>
                      {category.name}
                    </span>
                  );
                })}
              </div>
            </div>
            <span className="text-sm text-gray-500">
              {totalSelected} of {categories.length} selected
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
