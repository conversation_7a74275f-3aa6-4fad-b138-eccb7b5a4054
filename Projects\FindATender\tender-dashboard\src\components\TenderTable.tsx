'use client';

import React from 'react';
import { ProcessedTender } from '@/types/tender';
import { filterService, FILTER_CATEGORIES } from '@/services/filterService';
import { format, parseISO, isValid } from 'date-fns';
import { ExternalLink, Calendar, Building, Pound, Clock } from 'lucide-react';

interface TenderTableProps {
  tenders: ProcessedTender[];
  loading: boolean;
}

export function TenderTable({ tenders, loading }: TenderTableProps) {
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'Not specified';
    
    try {
      const date = parseISO(dateString);
      if (!isValid(date)) return 'Invalid date';
      return format(date, 'dd MMM yyyy');
    } catch {
      return 'Invalid date';
    }
  };

  const formatValue = (value: number | undefined, currency: string = 'GBP') => {
    if (!value) return 'Not specified';
    
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const getStatusBadge = (status: string) => {
    const statusColors: Record<string, string> = {
      'active': 'bg-green-100 text-green-800',
      'planned': 'bg-blue-100 text-blue-800',
      'tender': 'bg-yellow-100 text-yellow-800',
      'open': 'bg-green-100 text-green-800',
      'default': 'bg-gray-100 text-gray-800'
    };

    const colorClass = statusColors[status.toLowerCase()] || statusColors.default;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const getCategoryBadge = (tender: ProcessedTender) => {
    const topCategory = filterService.getTopCategory(tender);
    if (!topCategory) return null;

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium ${topCategory.color}`}>
        <span className="mr-1">{topCategory.icon}</span>
        {topCategory.name}
      </span>
    );
  };

  const getRelevanceScore = (score: number) => {
    const width = Math.min((score / 10) * 100, 100);
    const color = score >= 7 ? 'bg-green-500' : score >= 4 ? 'bg-yellow-500' : 'bg-blue-500';
    
    return (
      <div className="flex items-center space-x-2">
        <div className="w-16 bg-gray-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full ${color}`}
            style={{ width: `${width}%` }}
          />
        </div>
        <span className="text-xs text-gray-600">{score}</span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Loading Tenders...</h3>
        </div>
        <div className="p-6">
          <div className="animate-pulse space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (tenders.length === 0) {
    return (
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">No Tenders Found</h3>
        </div>
        <div className="p-6 text-center">
          <p className="text-gray-500">
            No sustainability-related tenders found matching your criteria.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">
          Tender Opportunities ({tenders.length})
        </h3>
      </div>
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Tender Details
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Buyer
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Dates
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Value
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Relevance
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {tenders.map((tender) => (
              <tr key={tender.id} className="hover:bg-gray-50">
                <td className="px-6 py-4">
                  <div className="space-y-2">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 line-clamp-2">
                        {tender.title}
                      </h4>
                      <p className="text-sm text-gray-600 line-clamp-2 mt-1">
                        {tender.description}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(tender.status)}
                      {getCategoryBadge(tender)}
                    </div>
                    {tender.cpvDescription && (
                      <p className="text-xs text-gray-500">
                        CPV: {tender.cpvDescription}
                      </p>
                    )}
                  </div>
                </td>
                
                <td className="px-6 py-4">
                  <div className="flex items-start space-x-2">
                    <Building className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {tender.buyer}
                      </p>
                      {tender.buyerContactEmail && (
                        <p className="text-xs text-gray-500">
                          {tender.buyerContactEmail}
                        </p>
                      )}
                    </div>
                  </div>
                </td>
                
                <td className="px-6 py-4">
                  <div className="space-y-1">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-600">Published:</span>
                      <span className="text-xs text-gray-900">
                        {formatDate(tender.publicationDate)}
                      </span>
                    </div>
                    {tender.closingDate && (
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3 text-red-400" />
                        <span className="text-xs text-gray-600">Closes:</span>
                        <span className="text-xs text-red-600 font-medium">
                          {formatDate(tender.closingDate)}
                        </span>
                      </div>
                    )}
                  </div>
                </td>
                
                <td className="px-6 py-4">
                  <div className="flex items-center space-x-1">
                    <Pound className="h-3 w-3 text-gray-400" />
                    <span className="text-sm text-gray-900">
                      {formatValue(tender.value, tender.currency)}
                    </span>
                  </div>
                </td>
                
                <td className="px-6 py-4">
                  {getRelevanceScore(tender.relevanceScore)}
                  {tender.matchedKeywords.length > 0 && (
                    <div className="mt-1">
                      <p className="text-xs text-gray-500">
                        Keywords: {tender.matchedKeywords.slice(0, 3).join(', ')}
                        {tender.matchedKeywords.length > 3 && '...'}
                      </p>
                    </div>
                  )}
                </td>
                
                <td className="px-6 py-4">
                  <a
                    href={tender.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    View Details
                    <ExternalLink className="ml-1 h-3 w-3" />
                  </a>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
