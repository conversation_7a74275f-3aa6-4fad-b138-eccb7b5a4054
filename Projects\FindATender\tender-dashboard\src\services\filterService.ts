import { ProcessedTender } from '@/types/tender';

export interface FilterCategory {
  id: string;
  name: string;
  keywords: string[];
  color: string;
  icon: string;
}

export interface FilterResult {
  category: string;
  score: number;
  matchedKeywords: string[];
}

export const FILTER_CATEGORIES: FilterCategory[] = [
  {
    id: 'net-zero',
    name: 'Net Zero & Climate',
    color: 'bg-green-100 text-green-800',
    icon: '🌱',
    keywords: [
      'net zero', 'carbon neutral', 'carbon reduction', 'carbon footprint', 'carbon offset',
      'greenhouse gas', 'ghg emissions', 'climate change', 'climate action',
      'decarbonisation', 'decarbonization', 'renewable energy', 'clean energy',
      'solar power', 'wind energy', 'wind farm', 'biomass', 'geothermal', 'hydroelectric',
      'energy storage', 'battery storage', 'hydrogen', 'fuel cell', 'electric vehicle',
      'ev charging', 'heat pump', 'carbon capture', 'ccus'
    ]
  },
  {
    id: 'sustainability',
    name: 'Sustainability',
    color: 'bg-blue-100 text-blue-800',
    icon: '♻️',
    keywords: [
      'sustainability', 'sustainable', 'environmental', 'eco-friendly', 'green',
      'circular economy', 'waste reduction', 'waste management', 'recycling',
      'energy efficiency', 'sustainable development', 'environmental management',
      'iso 14001', 'environmental impact', 'life cycle assessment', 'lca',
      'sustainable procurement', 'green building', 'breeam', 'leed',
      'sustainable transport', 'green finance', 'esg', 'environmental social governance'
    ]
  },
  {
    id: 'air-quality',
    name: 'Air Quality',
    color: 'bg-purple-100 text-purple-800',
    icon: '💨',
    keywords: [
      'air quality', 'air pollution', 'emissions', 'emission reduction',
      'particulate matter', 'pm2.5', 'pm10', 'nitrogen dioxide', 'no2',
      'nitrogen oxides', 'nox', 'ozone', 'clean air', 'air monitoring',
      'air quality monitoring', 'low emission', 'zero emission', 'ulez',
      'clean air zone', 'emission standards', 'euro 6', 'exhaust emissions',
      'vehicle emissions', 'industrial emissions', 'stack emissions'
    ]
  },
  {
    id: 'nature',
    name: 'Nature & Biodiversity',
    color: 'bg-emerald-100 text-emerald-800',
    icon: '🌿',
    keywords: [
      'biodiversity', 'nature', 'wildlife', 'habitat', 'ecosystem', 'conservation',
      'natural environment', 'green infrastructure', 'tree planting', 'rewilding',
      'nature recovery', 'species protection', 'environmental restoration',
      'woodland', 'forest', 'wetland', 'grassland', 'marine environment',
      'protected species', 'endangered species', 'habitat creation',
      'ecological survey', 'environmental impact assessment', 'natura 2000',
      'sites of special scientific interest', 'sssi', 'national park'
    ]
  }
];

export class FilterService {
  private normalizeText(text: string): string {
    return text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }

  private findKeywordMatches(text: string, keywords: string[]): string[] {
    const normalizedText = this.normalizeText(text);
    const matches: string[] = [];

    keywords.forEach(keyword => {
      const normalizedKeyword = this.normalizeText(keyword);
      if (normalizedText.includes(normalizedKeyword)) {
        matches.push(keyword);
      }
    });

    return matches;
  }

  public categorizeContent(content: string): FilterResult[] {
    const results: FilterResult[] = [];

    FILTER_CATEGORIES.forEach(category => {
      const matchedKeywords = this.findKeywordMatches(content, category.keywords);
      
      if (matchedKeywords.length > 0) {
        // Calculate score based on number of matches and keyword importance
        let score = 0;
        matchedKeywords.forEach(keyword => {
          // Higher score for more specific/important terms
          if (keyword.includes('net zero') || keyword.includes('carbon neutral')) {
            score += 5;
          } else if (keyword.includes('climate') || keyword.includes('sustainability')) {
            score += 3;
          } else if (keyword.includes('renewable') || keyword.includes('biodiversity')) {
            score += 2;
          } else {
            score += 1;
          }
        });

        results.push({
          category: category.id,
          score,
          matchedKeywords
        });
      }
    });

    return results.sort((a, b) => b.score - a.score);
  }

  public filterTenders(tenders: ProcessedTender[], categories: string[] = []): ProcessedTender[] {
    if (categories.length === 0) {
      return tenders;
    }

    return tenders.filter(tender => {
      const content = `${tender.title} ${tender.description}`;
      const results = this.categorizeContent(content);
      
      return results.some(result => categories.includes(result.category));
    });
  }

  public getTenderCategories(tender: ProcessedTender): FilterResult[] {
    const content = `${tender.title} ${tender.description}`;
    return this.categorizeContent(content);
  }

  public getTopCategory(tender: ProcessedTender): FilterCategory | null {
    const results = this.getTenderCategories(tender);
    if (results.length === 0) return null;

    const topResult = results[0];
    return FILTER_CATEGORIES.find(cat => cat.id === topResult.category) || null;
  }

  public searchTenders(tenders: ProcessedTender[], searchTerm: string): ProcessedTender[] {
    if (!searchTerm.trim()) {
      return tenders;
    }

    const normalizedSearch = this.normalizeText(searchTerm);
    
    return tenders.filter(tender => {
      const searchableContent = this.normalizeText(
        `${tender.title} ${tender.description} ${tender.buyer} ${tender.cpvDescription || ''}`
      );
      
      return searchableContent.includes(normalizedSearch);
    });
  }

  public sortTenders(tenders: ProcessedTender[], sortBy: 'relevance' | 'date' | 'value' | 'closing'): ProcessedTender[] {
    const sorted = [...tenders];

    switch (sortBy) {
      case 'relevance':
        return sorted.sort((a, b) => {
          if (a.relevanceScore !== b.relevanceScore) {
            return b.relevanceScore - a.relevanceScore;
          }
          return new Date(b.publicationDate).getTime() - new Date(a.publicationDate).getTime();
        });

      case 'date':
        return sorted.sort((a, b) => 
          new Date(b.publicationDate).getTime() - new Date(a.publicationDate).getTime()
        );

      case 'value':
        return sorted.sort((a, b) => {
          const aValue = a.value || 0;
          const bValue = b.value || 0;
          return bValue - aValue;
        });

      case 'closing':
        return sorted.sort((a, b) => {
          if (!a.closingDate && !b.closingDate) return 0;
          if (!a.closingDate) return 1;
          if (!b.closingDate) return -1;
          return new Date(a.closingDate).getTime() - new Date(b.closingDate).getTime();
        });

      default:
        return sorted;
    }
  }

  public getFilterStats(tenders: ProcessedTender[]): Record<string, number> {
    const stats: Record<string, number> = {};
    
    FILTER_CATEGORIES.forEach(category => {
      stats[category.id] = 0;
    });

    tenders.forEach(tender => {
      const categories = this.getTenderCategories(tender);
      categories.forEach(result => {
        stats[result.category]++;
      });
    });

    return stats;
  }
}

export const filterService = new FilterService();
