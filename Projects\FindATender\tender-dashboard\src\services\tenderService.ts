import axios from 'axios';
import { subDays, format, parseISO } from 'date-fns';
import { 
  TenderApiResponse, 
  OCDSRelease, 
  ProcessedTender, 
  TenderListResponse 
} from '@/types/tender';

const API_BASE_URL = 'https://www.find-tender.service.gov.uk/api/1.0';

// Sustainability-related keywords for filtering
const SUSTAINABILITY_KEYWORDS = [
  // Net Zero & Climate
  'net zero', 'carbon neutral', 'carbon reduction', 'carbon footprint', 'greenhouse gas',
  'climate change', 'decarbonisation', 'decarbonization', 'renewable energy',
  'solar', 'wind energy', 'biomass', 'geothermal', 'hydroelectric',
  
  // Sustainability
  'sustainability', 'sustainable', 'environmental', 'eco-friendly', 'green',
  'circular economy', 'waste reduction', 'recycling', 'energy efficiency',
  'sustainable development', 'environmental management', 'ISO 14001',
  
  // Air Quality
  'air quality', 'air pollution', 'emissions', 'particulate matter', 'PM2.5', 'PM10',
  'nitrogen dioxide', 'NO2', 'ozone', 'clean air', 'air monitoring',
  'emission reduction', 'low emission', 'zero emission',
  
  // Nature & Biodiversity
  'biodiversity', 'nature', 'wildlife', 'habitat', 'ecosystem', 'conservation',
  'natural environment', 'green infrastructure', 'tree planting', 'rewilding',
  'nature recovery', 'species protection', 'environmental restoration'
];

export class TenderService {
  private async fetchFromAPI(endpoint: string, params: Record<string, string> = {}): Promise<TenderApiResponse> {
    try {
      const url = `${API_BASE_URL}${endpoint}`;
      const response = await axios.get<TenderApiResponse>(url, { 
        params,
        timeout: 30000,
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'TenderDashboard/1.0'
        }
      });
      return response.data;
    } catch (error) {
      console.error('API fetch error:', error);
      throw new Error(`Failed to fetch tender data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private calculateRelevanceScore(tender: OCDSRelease): { score: number; keywords: string[] } {
    const text = [
      tender.tender?.title || '',
      tender.tender?.description || '',
      ...(tender.tender?.lots?.map(lot => lot.description) || []),
      ...(tender.tender?.items?.map(item => item.description || '') || [])
    ].join(' ').toLowerCase();

    const matchedKeywords: string[] = [];
    let score = 0;

    SUSTAINABILITY_KEYWORDS.forEach(keyword => {
      if (text.includes(keyword.toLowerCase())) {
        matchedKeywords.push(keyword);
        // Higher score for more specific terms
        if (keyword.includes('net zero') || keyword.includes('carbon neutral')) {
          score += 3;
        } else if (keyword.includes('sustainability') || keyword.includes('air quality')) {
          score += 2;
        } else {
          score += 1;
        }
      }
    });

    return { score, keywords: matchedKeywords };
  }

  private processRelease(release: OCDSRelease): ProcessedTender | null {
    const tender = release.tender;
    if (!tender) return null;

    // Only include open tenders (not awarded)
    if (tender.status === 'complete' || tender.status === 'cancelled') {
      return null;
    }

    const relevance = this.calculateRelevanceScore(release);
    
    // Only include tenders with sustainability relevance
    if (relevance.score === 0) {
      return null;
    }

    const buyer = release.parties?.find(party => party.roles.includes('buyer'));
    const buyerName = buyer?.name || release.buyer?.name || 'Unknown Buyer';
    
    // Find closing date from submission terms or lots
    let closingDate: string | undefined;
    if (tender.submissionTerms?.deadline) {
      closingDate = tender.submissionTerms.deadline;
    } else if (tender.lots && tender.lots.length > 0) {
      const lot = tender.lots[0];
      if (lot.contractPeriod?.endDate) {
        closingDate = lot.contractPeriod.endDate;
      }
    }

    // Calculate total value
    let totalValue: number | undefined;
    let currency = 'GBP';
    
    if (tender.value) {
      totalValue = tender.value.amount;
      currency = tender.value.currency;
    } else if (tender.lots && tender.lots.length > 0) {
      const lotValues = tender.lots
        .filter(lot => lot.value?.amount)
        .map(lot => lot.value!.amount);
      if (lotValues.length > 0) {
        totalValue = lotValues.reduce((sum, val) => sum + val, 0);
      }
    }

    return {
      id: release.id,
      ocid: release.ocid,
      title: tender.title,
      description: tender.description,
      buyer: buyerName,
      buyerContactEmail: buyer?.contactPoint?.email,
      publicationDate: release.date,
      closingDate,
      value: totalValue,
      currency,
      status: tender.status,
      url: `https://www.find-tender.service.gov.uk/Notice/${release.id}`,
      cpvCode: tender.classification?.id,
      cpvDescription: tender.classification?.description,
      isRelevant: true,
      relevanceScore: relevance.score,
      matchedKeywords: relevance.keywords
    };
  }

  async fetchRecentTenders(daysBack: number = 7, limit: number = 100, cursor?: string): Promise<TenderListResponse> {
    const endDate = new Date();
    const startDate = subDays(endDate, daysBack);
    
    const params: Record<string, string> = {
      updatedFrom: format(startDate, "yyyy-MM-dd'T'HH:mm:ss"),
      updatedTo: format(endDate, "yyyy-MM-dd'T'HH:mm:ss"),
      stages: 'tender',
      limit: limit.toString()
    };

    if (cursor) {
      params.cursor = cursor;
    }

    const response = await this.fetchFromAPI('/ocdsReleasePackages', params);
    
    const processedTenders: ProcessedTender[] = [];
    
    for (const release of response.releases) {
      const processed = this.processRelease(release);
      if (processed) {
        processedTenders.push(processed);
      }
    }

    // Sort by relevance score (highest first) then by publication date (newest first)
    processedTenders.sort((a, b) => {
      if (a.relevanceScore !== b.relevanceScore) {
        return b.relevanceScore - a.relevanceScore;
      }
      return new Date(b.publicationDate).getTime() - new Date(a.publicationDate).getTime();
    });

    return {
      tenders: processedTenders,
      totalCount: processedTenders.length,
      hasMore: response.releases.length === limit, // Simplified check
      cursor: undefined // Would need to extract from response headers in real implementation
    };
  }

  async fetchTenderById(noticeId: string): Promise<ProcessedTender | null> {
    try {
      const response = await this.fetchFromAPI(`/ocdsReleasePackages/${noticeId}`);
      
      if (response.releases.length === 0) {
        return null;
      }

      return this.processRelease(response.releases[0]);
    } catch (error) {
      console.error(`Error fetching tender ${noticeId}:`, error);
      return null;
    }
  }
}

export const tenderService = new TenderService();
