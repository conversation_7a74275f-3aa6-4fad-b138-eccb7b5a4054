// Types for the Find a Tender OCDS API
export interface TenderValue {
  amount: number;
  currency: string;
}

export interface TenderClassification {
  scheme: string;
  id: string;
  description: string;
}

export interface TenderPeriod {
  startDate?: string;
  endDate?: string;
  durationInDays?: number;
}

export interface TenderLot {
  id: string;
  title?: string;
  description: string;
  status: string;
  value?: TenderValue;
  contractPeriod?: TenderPeriod;
}

export interface TenderItem {
  id: string;
  description?: string;
  classification?: TenderClassification;
  additionalClassifications?: TenderClassification[];
  relatedLot?: string;
}

export interface TenderDocument {
  id: string;
  documentType: string;
  title?: string;
  description?: string;
  url?: string;
}

export interface TenderSubmissionTerms {
  deadline?: string;
  electronicSubmissionPolicy?: string;
}

export interface Tender {
  id: string;
  title: string;
  description: string;
  status: string;
  classification?: TenderClassification;
  mainProcurementCategory?: string;
  value?: TenderValue;
  lots?: TenderLot[];
  items?: TenderItem[];
  documents?: TenderDocument[];
  submissionTerms?: TenderSubmissionTerms;
  contractPeriod?: TenderPeriod;
  procurementMethod?: string;
  procurementMethodDetails?: string;
}

export interface PartyContactPoint {
  name?: string;
  email?: string;
  telephone?: string;
  url?: string;
}

export interface PartyAddress {
  streetAddress?: string;
  locality?: string;
  region?: string;
  postalCode?: string;
  countryName?: string;
}

export interface PartyIdentifier {
  scheme?: string;
  id?: string;
  legalName?: string;
}

export interface Party {
  id: string;
  name: string;
  identifier?: PartyIdentifier;
  address?: PartyAddress;
  contactPoint?: PartyContactPoint;
  roles: string[];
}

export interface Buyer {
  id: string;
  name: string;
}

export interface OCDSRelease {
  ocid: string;
  id: string;
  date: string;
  tag: string[];
  initiationType: string;
  tender?: Tender;
  parties?: Party[];
  buyer?: Buyer;
  language?: string;
}

export interface OCDSReleasePackage {
  uri: string;
  version: string;
  extensions?: string[];
  publishedDate: string;
  releases: OCDSRelease[];
}

export interface TenderApiResponse {
  uri: string;
  version: string;
  publishedDate: string;
  releases: OCDSRelease[];
}

// Processed tender data for the dashboard
export interface ProcessedTender {
  id: string;
  ocid: string;
  title: string;
  description: string;
  buyer: string;
  buyerContactEmail?: string;
  publicationDate: string;
  closingDate?: string;
  value?: number;
  currency?: string;
  status: string;
  url: string;
  cpvCode?: string;
  cpvDescription?: string;
  isRelevant: boolean;
  relevanceScore: number;
  matchedKeywords: string[];
}

// API response with pagination
export interface TenderListResponse {
  tenders: ProcessedTender[];
  totalCount: number;
  hasMore: boolean;
  cursor?: string;
}
